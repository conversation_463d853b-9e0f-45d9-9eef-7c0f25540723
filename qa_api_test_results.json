{"success": false, "passed_tests": 6, "total_tests": 7, "success_rate": 85.71428571428571, "status": "GOOD", "detailed_results": [{"test_name": "Authentication", "passed": true, "message": "Token received: eyJhbGciOiJIUzI1NiIs...", "timestamp": "2025-05-29T09:56:53.402759"}, {"test_name": "Health Check", "passed": true, "message": "Status: 200, Response: {'status': 'healthy'}", "timestamp": "2025-05-29T09:56:53.404648"}, {"test_name": "Current Schedule API", "passed": true, "message": "Retrieved 2 schedule items", "timestamp": "2025-05-29T09:56:53.409327"}, {"test_name": "Schedule Data Structure", "passed": true, "message": "All required fields present", "timestamp": "2025-05-29T09:56:53.409417"}, {"test_name": "Operating Rooms GET", "passed": true, "message": "Retrieved 80 operating rooms", "timestamp": "2025-05-29T09:56:53.415643"}, {"test_name": "OR Data Structure", "passed": true, "message": "All required fields present", "timestamp": "2025-05-29T09:56:53.416134"}, {"test_name": "Staff API GET", "passed": true, "message": "Retrieved 31 staff members", "timestamp": "2025-05-29T09:56:53.423682"}, {"test_name": "Staff Data Structure", "passed": true, "message": "All required fields present", "timestamp": "2025-05-29T09:56:53.424001"}, {"test_name": "SDST Matrix API", "passed": true, "message": "Retrieved SDST data with keys: ['surgery_types', 'setup_times', 'matrix']", "timestamp": "2025-05-29T09:56:53.429461"}, {"test_name": "Optimization API", "passed": false, "message": "Status: 500, Response: {\"detail\":\"Optimization failed: (pymysql.err.OperationalError) (1054, \\\"Unknown column 'surgeryequipmentusage.usage_start_time' in 'field list'\\\")\\n[SQL: SELECT surgeryequipmentusage.usage_id AS surgeryequipmentusage_usage_id, surgeryequipmentusage.surgery_id AS surgeryequipmentusage_surgery_id, surgeryequipmentusage.equipment_id AS surgeryequipmentusage_equipment_id, surgeryequipmentusage.usage_start_time AS surgeryequipmentusage_usage_start_time, surgeryequipmentusage.usage_end_time AS surgeryequipmentusage_usage_end_time \\nFROM surgeryequipmentusage \\nWHERE surgeryequipmentusage.surgery_id = %(surgery_id_1)s]\\n[parameters: {'surgery_id_1': 46}]\\n(Background on this error at: https://sqlalche.me/e/20/e3q8)\"}", "timestamp": "2025-05-29T09:56:53.457017"}, {"test_name": "Response Time /current", "passed": true, "message": "5.6ms", "timestamp": "2025-05-29T09:56:53.462895"}, {"test_name": "Response Time /operating-rooms", "passed": true, "message": "6.4ms", "timestamp": "2025-05-29T09:56:53.469514"}, {"test_name": "Response Time /staff", "passed": true, "message": "5.7ms", "timestamp": "2025-05-29T09:56:53.475458"}, {"test_name": "Response Time /sdst/matrix", "passed": true, "message": "4.3ms", "timestamp": "2025-05-29T09:56:53.480027"}]}